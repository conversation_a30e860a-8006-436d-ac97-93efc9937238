import 'package:flutter/material.dart';
import 'package:canne_connectee/features/auth/services/auth_service.dart';
import 'package:canne_connectee/shared/models/user.dart';
import 'package:canne_connectee/features/aveugle/pages/aveugle_home_page.dart';
import 'package:canne_connectee/features/proche/pages/proche_home_page.dart';
import 'package:canne_connectee/features/auth/pages/simple_user_type_selection_page.dart';

/// Widget qui route vers la bonne interface selon le type d'utilisateur
class UserTypeRouter extends StatefulWidget {
  const UserTypeRouter({super.key});

  @override
  State<UserTypeRouter> createState() => _UserTypeRouterState();
}

class _UserTypeRouterState extends State<UserTypeRouter> {
  final AuthService _authService = AuthService();
  bool _isLoading = true;
  User? _currentUser;

  @override
  void initState() {
    super.initState();
    _checkUserAuthentication();
  }

  Future<void> _checkUserAuthentication() async {
    try {
      // Vérifier si l'utilisateur est connecté
      final user = _authService.currentUser;
      
      if (user != null) {
        setState(() {
          _currentUser = user;
          _isLoading = false;
        });
      } else {
        // Essayer de charger les données utilisateur depuis le stockage local
        await _authService.loadUserFromStorage();
        final loadedUser = _authService.currentUser;
        
        setState(() {
          _currentUser = loadedUser;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Erreur lors de la vérification de l\'authentification: $e');
      setState(() {
        _currentUser = null;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: Color(0xFFF5F5F5),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF7900)),
              ),
              SizedBox(height: 16),
              Text(
                'Chargement...',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Si pas d'utilisateur connecté, aller à la sélection du type
    if (_currentUser == null) {
      return const SimpleUserTypeSelectionPage();
    }

    // Router vers la bonne interface selon le type d'utilisateur
    switch (_currentUser!.userType) {
      case UserType.aveugle:
        return const AveuglesHomePage();
      case UserType.proche:
        return const ProcheHomePage();
    }
  }
}

/// Widget de transition avec animation
class UserTypeTransition extends StatefulWidget {
  final Widget child;
  final UserType userType;

  const UserTypeTransition({
    super.key,
    required this.child,
    required this.userType,
  });

  @override
  State<UserTypeTransition> createState() => _UserTypeTransitionState();
}

class _UserTypeTransitionState extends State<UserTypeTransition>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: widget.child,
          ),
        );
      },
    );
  }
}

/// Widget d'écran de démarrage avec logo
class SplashScreen extends StatefulWidget {
  final VoidCallback onComplete;

  const SplashScreen({
    super.key,
    required this.onComplete,
  });

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    ));

    _controller.forward().then((_) {
      Future.delayed(const Duration(milliseconds: 1000), () {
        widget.onComplete();
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFF7900),
      body: Center(
        child: AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(60),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.accessibility_new,
                        size: 60,
                        color: Color(0xFFFF7900),
                      ),
                    ),
                    const SizedBox(height: 30),
                    const Text(
                      'Canne Connectée',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Assistance intelligente',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
