// Script pour vérifier les erreurs de compilation
import 'dart:io';

void main() async {
  print('Vérification des erreurs de compilation...');
  
  // Vérifier les imports principaux
  try {
    // Test des imports principaux
    print('Test des imports...');
    
    // Vérifier si les fichiers existent
    final mainFile = File('lib/main.dart');
    if (!mainFile.existsSync()) {
      print('ERREUR: lib/main.dart n\'existe pas');
      return;
    }
    
    final userModel = File('lib/shared/models/user.dart');
    if (!userModel.existsSync()) {
      print('ERREUR: lib/shared/models/user.dart n\'existe pas');
      return;
    }
    
    final authService = File('lib/features/auth/services/auth_service.dart');
    if (!authService.existsSync()) {
      print('ERREUR: lib/features/auth/services/auth_service.dart n\'existe pas');
      return;
    }
    
    print('✅ Tous les fichiers principaux existent');
    
    // Vérifier le contenu des fichiers pour les erreurs communes
    final mainContent = await mainFile.readAsString();
    if (!mainContent.contains('import \'package:flutter/material.dart\';')) {
      print('ERREUR: Import Flutter manquant dans main.dart');
    }
    
    print('✅ Vérification terminée');
    
  } catch (e) {
    print('ERREUR lors de la vérification: $e');
  }
}
